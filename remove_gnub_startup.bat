@echo off
echo ===================================
echo Removing 'gnub' from Windows Startup
echo ===================================
echo.

echo Checking registry entries...
echo.

REM Remove from Current User Run key
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v gnub /f 2>nul
if %errorlevel%==0 echo Removed gnub from Current User Run registry

REM Remove from Current User RunOnce key  
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\RunOnce" /v gnub /f 2>nul
if %errorlevel%==0 echo Removed gnub from Current User RunOnce registry

REM Remove from Local Machine Run key (requires admin)
reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v gnub /f 2>nul
if %errorlevel%==0 echo Removed gnub from Local Machine Run registry

REM Remove from Local Machine RunOnce key (requires admin)
reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\RunOnce" /v gnub /f 2>nul
if %errorlevel%==0 echo Removed gnub from Local Machine RunOnce registry

REM Remove from WOW6432Node (32-bit apps on 64-bit Windows)
reg delete "HKLM\Software\WOW6432Node\Microsoft\Windows\CurrentVersion\Run" /v gnub /f 2>nul
if %errorlevel%==0 echo Removed gnub from WOW6432Node Run registry

reg delete "HKLM\Software\WOW6432Node\Microsoft\Windows\CurrentVersion\RunOnce" /v gnub /f 2>nul
if %errorlevel%==0 echo Removed gnub from WOW6432Node RunOnce registry

echo.
echo Checking startup folders...
echo.

REM Check and remove from user startup folder
if exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\*gnub*" (
    del "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\*gnub*" /q
    echo Removed gnub files from user startup folder
)

REM Check and remove from all users startup folder
if exist "%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Startup\*gnub*" (
    del "%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Startup\*gnub*" /q
    echo Removed gnub files from all users startup folder
)

echo.
echo ===================================
echo Script completed!
echo.
echo If gnub still starts with Windows:
echo 1. Open Task Manager (Ctrl+Shift+Esc)
echo 2. Go to Startup tab
echo 3. Find and disable gnub
echo.
echo OR
echo.
echo 1. Open Windows Settings
echo 2. Go to Apps ^> Startup  
echo 3. Find and disable gnub
echo ===================================
echo.
pause
